﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net10.0-android;</TargetFrameworks>
		
		<OutputType>Exe</OutputType>
		<RootNamespace>ModelFury.Briefly.MobileApp</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>Briefly AI News</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.modelfury.brieflyai</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
		<WindowsPackageType>None</WindowsPackageType>

		<!--<MauiEnableXamlCBindingWithSourceCompilation>true</MauiEnableXamlCBindingWithSourceCompilation>-->
		<!--<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<MauiStrictXamlCompilation>true</MauiStrictXamlCompilation>-->

		 <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
 	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-android|AnyCPU'">
	  <AndroidPackageFormat>apk</AndroidPackageFormat>
	  <RunAOTCompilation>True</RunAOTCompilation>
	  <PublishTrimmed>True</PublishTrimmed>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net9.0-android|AnyCPU'">
	  <EmbedAssembliesIntoApk>False</EmbedAssembliesIntoApk>
	  <RunAOTCompilation>False</RunAOTCompilation>
	</PropertyGroup>
	<ItemGroup>
		<!-- App Icon -->

		<!-- Splash Screen -->

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" /> 
	</ItemGroup>

	<ItemGroup>
	  <None Remove="Resources\AppIcon\appicon.svg" />
	  <None Remove="Resources\AppIcon\appiconfg.svg" />
	  <None Remove="Resources\AppIcon\briefly.svg" />
	  <None Remove="Resources\Splash\splash.svg" />
	</ItemGroup>

	<ItemGroup>
	  <MauiSplashScreen Include="Resources\Splash\splash.svg" />
	</ItemGroup>

	<ItemGroup>
	  <MauiIcon Include="Resources\AppIcon\appicon.svg" />
	  <MauiIcon Include="Resources\AppIcon\appiconfg.svg" />
	  <MauiIcon Include="Resources\AppIcon\briefly.svg" />
	</ItemGroup> 
	<ItemGroup>
		<!-- Fixed: Updated package versions to match dependencies -->
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="10.0.0-preview.7.25380.108" />
		<PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="10.0.0-preview.7.25380.108" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="10.0.0-preview.7.25380.108" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="10.0.0-preview.7.25380.108" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="10.0.0-preview.7.25406.3" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="10.0.0-preview.7.25380.108" />
		<PackageReference Include="CommunityToolkit.Maui" Version="12.2.0" />
		
		<PackageReference Include="Xamarin.Google.Guava.ListenableFuture" Version="********" /> 
	</ItemGroup>
 

	<!-- platform assets -->
	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">
		<GoogleServicesJson Include="google-services.json" />
	</ItemGroup>
	
	<ItemGroup>
		<ProjectReference Include="..\DeepMessage.Framework.Core\Platform.Framework.Core.csproj" />
		<ProjectReference Include="..\DeepMessage.MauiShared\Platform.Framework.Maui.csproj" />
		<ProjectReference Include="..\DeepMessage.ServiceContracts\DeepMessage.ServiceContracts.csproj" />
		<ProjectReference Include="..\Platform.Client.Common\Platform.Client.Common.csproj" />
		<ProjectReference Include="..\Platform.Client.Data\Platform.Client.Data.csproj" />
		<!-- Added missing project references from HybridApp -->
		<ProjectReference Include="..\Platform.Client.Services\Platform.Client.Services.csproj" />
	</ItemGroup>
	
	<ItemGroup>
	  <Compile Update="Features\Home\NewsListingComponent.xaml.cs">
	    <DependentUpon>NewsListingComponent.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>



	 
</Project>
