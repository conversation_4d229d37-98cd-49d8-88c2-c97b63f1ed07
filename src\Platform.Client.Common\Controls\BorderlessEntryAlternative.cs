using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using Microsoft.Maui.Controls.Platform;
using Android.Content.Res;


#if ANDROID
using Android.Graphics.Drawables;
using Android.Graphics;
#endif

namespace Platform.Client.Common.Controls
{
    /// <summary>
    /// Alternative BorderlessEntry implementation for .NET 10 compatibility
    /// Uses a different approach that works better with Material Components
    /// </summary>
    public class BorderlessEntryAlternative : Entry
    {
        public BorderlessEntryAlternative()
        {
            BackgroundColor = Colors.Transparent;
        }
    }

    /// <summary>
    /// Alternative handler that creates a transparent drawable programmatically
    /// </summary>
    public class BorderlessEntryAlternativeHandler : EntryHandler
    {
        public BorderlessEntryAlternativeHandler() : base()
        {
        }

        protected override void ConnectHandler(MauiAppCompatEditText platformView)
        {
            base.ConnectHandler(platformView);

#if ANDROID
            if (platformView != null)
            {
                try
                {
                    // Create a transparent drawable programmatically
                    var transparentDrawable = new ColorDrawable(Android.Graphics.Color.Transparent);
                    platformView.SetBackground(transparentDrawable);
                    
                    // Ensure background tint is also transparent
                    PlatformView.BackgroundTintList = ColorStateList.ValueOf(Android.Graphics.Color.Transparent);
                    
                    // Remove any padding that might show borders
                    platformView.SetPadding(0, 0, 0, 0);
                }
                catch (System.Exception ex)
                {
                    // Ultimate fallback
                    System.Diagnostics.Debug.WriteLine($"BorderlessEntryAlternative error: {ex.Message}");
                    platformView.SetBackgroundColor(Android.Graphics.Color.Transparent);
                }
            }
#elif IOS
            if (platformView != null)
            {
                platformView.Layer.BorderWidth = 0;
                platformView.BackgroundColor = UIKit.UIColor.Clear;
            }
#elif WINDOWS
            if (platformView != null)
            {
                platformView.BorderThickness = new Microsoft.UI.Xaml.Thickness(0);
                platformView.Background = null;
            }
#endif
        }
    }
}
