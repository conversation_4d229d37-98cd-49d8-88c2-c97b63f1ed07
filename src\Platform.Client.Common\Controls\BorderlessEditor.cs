using AndroidX.AppCompat.Widget;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;

namespace Platform.Client.Common.Controls
{
    /// <summary>
    /// Custom Editor control that removes the default platform underline/border
    /// </summary>
    public class BorderlessEditor : Editor
    {
        public BorderlessEditor()
        {
            BackgroundColor = Colors.Transparent;
        }
    }

    /// <summary>
    /// Custom handler for BorderlessEditor to remove platform-specific underlines
    /// </summary>
    public class BorderlessEditorHandler : EditorHandler
    {
        public BorderlessEditorHandler() : base()
        {
        }
        protected override void ConnectHandler(MauiAppCompatEditText platformView)
        {
            base.ConnectHandler(platformView);


#if ANDROID
            // Remove underline on Android
            if (platformView != null)
            {
                platformView.Background = null;
                platformView.SetBackgroundColor(Android.Graphics.Color.Transparent);
            }
#elif IOS
            // Remove border on iOS
            if (platformView != null)
            {
                platformView.Layer.BorderWidth = 0;
                platformView.BackgroundColor = UIKit.UIColor.Clear;
            }
#elif WINDOWS
            // Remove border on Windows
            if (platformView != null)
            {
                platformView.BorderThickness = new Microsoft.UI.Xaml.Thickness(0);
                platformView.Background = null;
            }
#endif
        }

    }

    /// <summary>
    /// Custom Entry control that removes the default platform underline/border
    /// </summary>
    public class BorderlessEntry : Entry
    {
        public BorderlessEntry()
        {
            BackgroundColor = Colors.Transparent;
        }
    }

    /// <summary>
    /// Custom handler for BorderlessEntry to remove platform-specific underlines
    /// </summary>
    public class BorderlessEntryHandler : EntryHandler
    {
        public BorderlessEntryHandler() : base()
        {
        }
 
        protected override void ConnectHandler(MauiAppCompatEditText platformView)
        {
            base.ConnectHandler(platformView);


#if ANDROID
            // Remove underline on Android
            if (platformView != null)
            {
                
                platformView.Background = null;
                platformView.SetBackgroundColor(Android.Graphics.Color.Transparent);
                PlatformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(Colors.Transparent.ToAndroid());
                //#endif
            }
#elif IOS
            // Remove border on iOS
            if (platformView != null)
            {
                platformView.Layer.BorderWidth = 0;
                platformView.BackgroundColor = UIKit.UIColor.Clear;
            }
#elif WINDOWS
            // Remove border on Windows
            if (platformView != null)
            {
                platformView.BorderThickness = new Microsoft.UI.Xaml.Thickness(0);
                platformView.Background = null;
            }
#endif
        }
    }
}
